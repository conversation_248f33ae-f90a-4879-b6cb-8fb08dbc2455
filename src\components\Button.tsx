import React from "react";

interface ButtonProps {
  text: string;
  type?: "button" | "submit" | "reset";
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({ text, type, ...probs }) => {
  return (
    <button {...probs} type={type} className="w-full bg-gradient-to-r from-[#FF7000] to-[#E2985E] text-white py-3 px-4 rounded-lg font-medium hover:opacity-80 transition-colors mb-6">
      {text}
    </button>
  );
};
export default Button;
