import Image from "next/image";
import Logo from "../../public/Logo.png";
import SearchIcon from "../../public/Magnifer.png";
import LightIcon from "../../public/Union.png";
import JsIcon from "../../public/js.png";

const Header = () => {
  return (
    <header className="w-full px-4 py-4 flex flex-wrap items-center justify-between gap-4 bg-white border-b border-gray-300 rounded-t-2xl">
      
      {/* Logo */}
      <div className="flex-shrink-0">
        <Image src={Logo} alt="logo" className="w-40 object-contain" />
      </div>

      {/* Search Bar */}
      <div className="relative flex-grow max-w-xl w-full">
        <Image
          src={SearchIcon}
          alt="search"
          className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 object-contain"
        />
        <input
          type="text"
          className="w-full border border-gray-300 rounded-xl py-3 pl-10 pr-4 bg-[#f4f7f9] text-gray-500 focus:outline-none"
          placeholder="Search for questions..."
        />
      </div>

      {/* Icons */}
      <div className="flex gap-2 items-center flex-shrink-0">
        <Image src={LightIcon} alt="Light Mode" className="w-6 h-6 object-contain" />
        <Image src={JsIcon} alt="JS Icon" className="w-6 h-6 object-contain" />
      </div>
    </header>
  );
};

export default Header;
