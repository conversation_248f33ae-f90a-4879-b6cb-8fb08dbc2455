'use client'

import AuthHeader from "@/components/AuthHeader";
import Button from "@/components/Button";
import SocialBtn from "@/components/SocialBtn";
import upArrow from "../../../public/up-arrow.png";
import { useRouter } from "next/navigation";

const ResendEmail = () => {
    const router = useRouter();
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-visible py-10">
      <div className="bg-[#f4f7f9] rounded-lg shadow-lg p-8 w-full max-w-lg mx-2 relative">
        <AuthHeader
          title="Resend Email"
          text="We sent a password reset <NAME_EMAIL>"
          
        />
        <div className="mt-5">

        <Button text="Resend Email" />
        <SocialBtn text="back to login" icon={upArrow} onClick={() => router.push("/login")} />
        </div>
      </div>
    </div>
  );
};

export default ResendEmail;
