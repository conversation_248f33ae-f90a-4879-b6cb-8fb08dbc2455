
"use client";
import React, { useState } from "react";
import Image from "next/image";
import Icon from "../../../public/icon.png";
import Button from "@/components/Button";
import Link from "next/link";
import SocialBtn from "@/components/SocialBtn";
import GoogleIcon from "../../../public/google.png";
import GITICON from "../../../public/gh.png";
import AuthHeader from "@/components/AuthHeader";
import { useRouter } from "next/navigation";
const signupPage = () => {
  const [name,setName]=useState("");
  const [email,setEmail]=useState("");
  const [password,setPassword]=useState("");
  const [nameError,setNameError]=useState("");
  const [emailError,setEmailError]=useState("");
  const [passwordError,setPasswordError]=useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleNameChange=(e:React.ChangeEvent<HTMLInputElement>)=>{
    const value=e.target.value;
    setName(value);
    if(!value || value.trim()===""){
      setNameError("Name is required");
    }
    else if(value.length<3){
      setNameError("Name must be at least 3 characters");
    }
    else{
      setNameError("");
    }
  }
  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };
  const handleEmailChange=(e:React.ChangeEvent<HTMLInputElement>)=>{
    const value=e.target.value;
    setEmail(value);
    if(!value || value.trim()===""){
      setEmailError("Email is required");
    }
    else if(!validateEmail(value)){
      setEmailError("Enter a valid email address");
    }
    else{
      setEmailError("");
    }
  }
  const handlePasswordChange=(e:React.ChangeEvent<HTMLInputElement>)=>{
    const value=e.target.value;
    setPassword(value);
    if(!value || value.trim()===""){
      setPasswordError("Password is required");
    }
    else if(value.length<6){
      setPasswordError("Password must be at least 6 characters");
    }
    else{
      setPasswordError("");
    }
  }
  const handleSubmit=(e:React.FormEvent<HTMLFormElement>)=>{
    e.preventDefault();
    if(!name ){
      setNameError("Name is required");
      return;
    }
    if(!email || !validateEmail(email)){
      setEmailError("Please enter a valid email");
      return;
    }
    if(!password || password.trim()===""){
      setPasswordError("Password is required");
      return;
    }
    if(password.length<6){
      setPasswordError("Password must be at least 6 characters");
      return;
    }
    if(!name && !email &&!password){
      setNameError("Name is required");
      setEmailError("Email is required");
      setPasswordError("Password is required");
      return;
    }
    setIsLoading(true);
    router.push("/home");
    setIsLoading(false);
    setName("");
    setEmail("");
    setPassword("");
  }
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-visible py-10">
      <div className="bg-[#f4f7f9] rounded-lg shadow-lg p-8 w-full max-w-md mx-4 relative">
        <AuthHeader title="Create your account" text="to continue to DevFlow" icon={Icon}/>
        <form onSubmit={handleSubmit}>
        <div className="mt-10">
          <div className="mb-6 ">
            <label
              htmlFor="name"
              className="block text-[16px] font-medium  mb-2"
            >
              Username
            </label>
            <input
              type="text"
              id="username"
              value={name}
              onChange={handleNameChange}

              className="w-full px-3 py-3 border bg-white border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder=""
            />
            {nameError && <p className="text-red-500 text-sm mb-2 mt-2">{nameError}</p>}
          </div>

          <div className="mb-6 ">
            <label
              htmlFor="email"
              className="block text-[16px] font-medium  mb-2"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              className={`w-full px-3 py-3 border ${emailError ? "border-red-500" : "border-gray-300"} bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent`}
              placeholder=""
            />
            {emailError && <p className="text-red-500 text-sm mb-2 mt-2">{emailError}</p>}
          </div>
          <div className="mb-6 ">
            <label
              htmlFor="password"
              className="block text-[16px] font-medium  mb-2"
            >
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword?"text":"password"}
                id="password"
                value={password}
                onChange={handlePasswordChange}
                className="w-full px-3 py-3 border border-gray-300 bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder=""
              />
            <button type="button" className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500" onClick={() => setShowPassword((prev) => !prev)}> {showPassword ? "Hide" : "Show"}</button>
          </div>
            {passwordError && <p className="text-red-500 text-sm mb-2 mt-2">{passwordError}</p>}
          </div>
        </div>
        <Button text={isLoading ? "Loading..." : "Continue"} disabled={isLoading} />
        </form>
        <p className="flex justify-center">
          Already have an account?
          <Link
            href="/login"
            className="text-orange-500 hover:text-orange-600 font-medium pl-2"
          >
            Sign in
          </Link>
        </p>
        <div className="flex gap-2 mt-5">

        <SocialBtn icon={GITICON} text="Sign up with Github" />
        <SocialBtn icon={GoogleIcon} text="Sign up with Google" />
        </div>
      </div>
    </div>
  );
};

export default signupPage;
