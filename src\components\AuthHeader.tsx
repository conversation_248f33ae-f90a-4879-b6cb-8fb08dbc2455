import React from "react";
import Image, { StaticImageData } from "next/image";

interface HeaderProps{
    title:string,
    text:string,
    icon?:string |StaticImageData
}
const AuthHeader: React.FC<HeaderProps>=({title,text,icon}) => {
    return(
         <div className="flex">
                  <div className="w-5/4">
                    <h1 className="text-2xl font-bold">{title}</h1>
                    <p className="text-gray-600 text-lg">{text}</p>
                  </div>
                  <div>
                    {icon && (
                      <Image
                        src={icon}
                        alt="icon"
                        className="w-14 h-14 self-end object-contain"
                      />
                    )}
                  </div>
                </div>
    )}
    export default AuthHeader;