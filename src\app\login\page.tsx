"use client";
import AuthHeader from "@/components/AuthHeader";
import React, { useState } from "react";
import Icon from "../../../public/icon.png";
import Button from "@/components/Button";
import Link from "next/link";
import SocialBtn from "@/components/SocialBtn";
import GoogleIcon from "../../../public/google.png";
import GITICON from "../../../public/gh.png";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    if (!value) {
      setEmailError("Email is required");
    } else if (!validateEmail(value)) {
      setEmailError("Enter a valid email address");
    } else {
      setEmailError("");
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!email || !validateEmail(email)) {
      setEmailError("Please enter a valid email");
      return;
    }

    setIsLoading(true);
    console.log("email:", email, "password:", password);
    toast.success("Login successful");

    setTimeout(() => {
      setIsLoading(false);
      setEmail("");
      setPassword("");
      router.push("/home");
      setEmailError("");
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-visible py-10">
      <div className="bg-[#f4f7f9] rounded-lg shadow-lg p-8 w-full max-w-md mx-4 relative">
        <AuthHeader title="Sign in" text="to continue to DevFlow" icon={Icon} />

        <form onSubmit={handleSubmit}>
          <div className="mt-8">
            <div className="mb-6">
              <label htmlFor="email" className="block text-[16px] font-medium mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={handleEmailChange}
                onBlur={() => {
                  if (!email) {
                    setEmailError("Email is required");
                  } else if (!validateEmail(email)) {
                    setEmailError("Enter a valid email address");
                  }
                }}
                className={`w-full px-3 py-3 border ${
                  emailError ? "border-red-500" : "border-gray-300"
                } bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent`}
                required
              />
              {emailError && <p className="text-red-500 text-sm mb-2 mt-2">{emailError}</p>}
            </div>

            <div className="mb-3">
              <label htmlFor="password" className="block text-[16px] font-medium mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500"
                  onClick={() => setShowPassword((prev) => !prev)}
                >
                  {showPassword ? "Hide" : "Show"}
                </button>
              </div>
            </div>

            <p className="flex justify-end mb-5">
              <Link href="/forget-password" className="text-[#1DA1F2] hover:text-orange-600 font-medium text-sm">
                Forgot password?
              </Link>
            </p>

            <Button text={isLoading ? "Loading..." : "Continue"} disabled={isLoading} />

            <p className="flex justify-center mt-4 text-sm">
              Don’t have an account?
              <Link href="/signUp" className="text-orange-500 hover:text-orange-600 font-medium pl-2">
                Sign up
              </Link>
            </p>
          </div>
        </form>

        <div className="flex gap-1 mt-5">
          <SocialBtn icon={GITICON} text="Sign up with GitHub" />
          <SocialBtn icon={GoogleIcon} text="Sign up with Google" onClick={() => router.push("/signIn")} />
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
