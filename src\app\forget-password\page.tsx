"use client";
import { toast } from "react-toastify";
import AuthHeader from "@/components/AuthHeader";
import React, { useState } from "react";
import Icon from "../../../public/icon.png";
import Button from "@/components/Button";
import SocialBtn from "@/components/SocialBtn";
import upArrow from "../../../public/up-arrow.png";
import Link from "next/link";
import { useRouter } from "next/navigation";


const ForgetPassword = () => {
  const [email, setEmail] = useState("");
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    toast.success("Email sent successfully");
    console.log("email sent");
    setEmail("");
    router.push("/resendEmail");
    console.log("navigated to resend email");
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-visible py-10">
      <div className="bg-[#f4f7f9] rounded-lg shadow-lg p-8 w-full max-w-lg mx-2 relative">
        <AuthHeader
          title="Forget password"
          text="No worries, we’ll send you reset instructions."
          icon={Icon}
        />

        <form onSubmit={handleSubmit}>
          <div className="mt-8">
            <div className="mb-6">
              <label
                htmlFor="email"
                className="block text-[16px] font-medium mb-2"
              >
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-3 border bg-white border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          <Button text="Continue" type="submit" />
        </form>

        <SocialBtn text="back to login" icon={upArrow} />
      </div>
    </div>
  );
};

export default ForgetPassword;
