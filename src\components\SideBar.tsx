'use client';
import { useState } from "react";
import { useRouter } from "next/navigation";
import { House, Star, BriefcaseBusiness, Tag, Users, FileQuestionMark, LogOut } from "lucide-react";
const SideBar = () => {
  const router = useRouter();
   const sidebarItems = [
    { name: "Home", icon: <House /> },
    { name: "Collections", icon: <Star /> },
    { name: "Find Jobs", icon: <BriefcaseBusiness /> },
    { name: "Tags", icon: <Tag /> },
    { name: "communities", icon: <Users /> },
    { name: "Ask a Question", icon: <FileQuestionMark /> },
  ];

  const [activeTab, setActiveTab] = useState("Home");
  return (
    <aside className="w-1/5 h-125 bg-white border-r border-gray-200 relative">
            <div className="p-6 space-y-4">
              {sidebarItems.map((item, idx) => {
                const isActive = item.name === activeTab;
                return (
                  <div
                    key={idx}
                    onClick={() => setActiveTab(item.name)}
                    className={`rounded-xl p-3 cursor-pointer transition ${
                      isActive
                        ? "text-white bg-gradient-to-r from-[#FF7000] to-[#E2985E]"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <div className="flex gap-2">
                      {item.icon}
                      <p>{item.name}</p>
                    </div>
                  </div>
                );
              })}
              <div className="absolute bottom-0 left-0 w-full p-6">
                <button onClick={()=>router.push("/login")} className="w-full flex items-center gap-2 hover:bg-gradient-to-r from-[#FF7000] to-[#E2985E] hover:text-white text-gray-500 py-3 px-4 rounded-lg font-medium transition-colors">
                  <LogOut className="w-5 h-5" />
                  Logout
                </button>
              </div>
            </div>
          </aside>
  )}
export default SideBar;