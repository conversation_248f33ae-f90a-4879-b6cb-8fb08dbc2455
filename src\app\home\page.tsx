"use client";

import Header from "@/components/Header";
import SideBar from "@/components/SideBar";
import { FileQuestionMark } from "lucide-react";

const HomePage = () => {
  const questions = [
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "Would it be appropriate to point out an error in another paper during a referee report?" },
    { icon: <FileQuestionMark className="w-6 h-6 text-blue-500" />, text: "How can an air conditioning machine exist?" },
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "Interrogated every time crossing UK Border as citizen" },
    { icon: <FileQuestionMark className="w-6 h-6 text-blue-500" />, text: "Low digit addition generator" },
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "What is an example of 3 numbers that do not make up a vector?" }
  ];

  return (
    <div className="relative h-screen flex flex-col">
      <Header />
      <div className="flex flex-1">
        {/* Sidebar */}
        <SideBar />

        <main className="flex-1 p-4 overflow-y-auto"> 
         
        </main>

        {/* Right Sidebar */}
        <aside className="hidden lg:block w-full max-w-xs h-full bg-white shadow-lg z-10 p-4 border-l border-gray-200 overflow-y-auto scrollbar-hide">
          <div className="space-y-4">
            <h1 className="text-2xl font-bold mt-5">Hot Network</h1>
            <div className="space-y-5">
              {questions.map((item, idx) => (
                <div key={idx} className="flex gap-3">
                  <span>{item.icon}</span>
                  <p className="text-gray-700 font-antialiased text-sm">{item.text}</p>
                </div>
              ))}
            </div>

            <div className="mt-10 pb-10">
              <h1 className="text-2xl font-bold mb-4">Popular Tags</h1>
              <div className="space-y-5 pb-20">
                {questions.map((item, idx) => (
                  <div key={idx} className="flex gap-3">
                    <span>{item.icon}</span>
                    <p className="text-gray-700 font-antialiased text-sm">{item.text}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
};

export default HomePage;
