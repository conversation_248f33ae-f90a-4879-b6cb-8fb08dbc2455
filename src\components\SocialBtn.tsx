import React from "react";
import Image, { StaticImageData } from "next/image";

interface ButtonProps{
    icon: string |StaticImageData;
    text: string;
    onClick?: any;
}

const SocialBtn: React.FC<ButtonProps> = ({icon,text,...probs}) => {
  return (
    <div className="flex justify-center items-center gap-2 rounded-lg  px-5 py-3 bg-white">
      <Image
        src={icon}
        alt="icon"
        className="w-4 h-4  object-contain"
      />
      <button {...probs}>
        <p className="text-sm">{text}</p>
      </button>
    </div>
  );
};

export default SocialBtn;